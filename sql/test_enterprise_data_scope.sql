-- 测试本企业数据权限脚本
-- 用于验证新增的企业数据权限是否正常工作

-- 1. 查看当前所有数据权限类型
SELECT 
    '1' as data_scope_value, '所有数据权限' as data_scope_name
UNION ALL
SELECT 
    '2' as data_scope_value, '自定义数据权限' as data_scope_name
UNION ALL
SELECT 
    '3' as data_scope_value, '本单位数据权限+企业数据权限' as data_scope_name
UNION ALL
SELECT 
    '4' as data_scope_value, '本单位及以下数据权限+企业数据权限' as data_scope_name
UNION ALL
SELECT 
    '5' as data_scope_value, '仅本人数据权限' as data_scope_name
UNION ALL
SELECT 
    '6' as data_scope_value, '本企业数据权限' as data_scope_name;

-- 2. 查看企业用户信息
SELECT 
    u.user_id,
    u.user_name,
    u.nick_name,
    u.org_id,
    d.dept_name as org_name,
    d.org_type,
    CASE d.org_type 
        WHEN '0' THEN '部门'
        WHEN '1' THEN '单位'
        WHEN '2' THEN '企业'
        ELSE '未知'
    END as org_type_name
FROM sys_user u
LEFT JOIN sys_dept d ON u.org_id = d.dept_id
WHERE d.org_type = '2'  -- 只查看企业用户
ORDER BY u.org_id, u.user_name;

-- 3. 模拟本企业数据权限的SQL生成
-- 假设用户org_id = 234（广西交科集团有限公司），测试生成的SQL条件

-- 本企业数据权限 (DATA_SCOPE_ENTERPRISE = "6")
-- 生成的SQL应该类似：
-- AND (d.dept_id = 234 AND EXISTS (SELECT 1 FROM sys_dept WHERE dept_id = 234 AND org_type = '2'))

-- 4. 验证企业权限查询
-- 测试查询企业234的数据（模拟权限过滤）
SELECT 
    d.dept_id,
    d.dept_name,
    d.org_type,
    CASE d.org_type 
        WHEN '0' THEN '部门'
        WHEN '1' THEN '单位'
        WHEN '2' THEN '企业'
        ELSE '未知'
    END as org_type_name
FROM sys_dept d
WHERE (d.dept_id = 234 AND EXISTS (SELECT 1 FROM sys_dept WHERE dept_id = 234 AND org_type = '2'));

-- 5. 验证非企业用户无法通过企业权限查看数据
-- 假设用户org_id = 100（某个单位），测试企业权限查询
SELECT 
    d.dept_id,
    d.dept_name,
    d.org_type
FROM sys_dept d
WHERE (d.dept_id = 100 AND EXISTS (SELECT 1 FROM sys_dept WHERE dept_id = 100 AND org_type = '2'));
-- 这个查询应该返回空结果，因为dept_id=100不是企业

-- 6. 创建测试角色（可选）
-- INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, status, del_flag, create_by, create_time, remark)
-- VALUES ('企业用户', 'enterprise_user', 6, '6', '0', '0', 'admin', NOW(), '本企业数据权限测试角色');

-- 7. 验证完整的权限过滤逻辑
-- 模拟告警表的企业权限查询（告警表使用org_id字段）
SELECT 
    'alarm_test' as table_name,
    '模拟告警数据' as description
WHERE (234 = 234 AND EXISTS (SELECT 1 FROM sys_dept WHERE dept_id = 234 AND org_type = '2'));

-- 8. 查看所有企业列表（用于权限测试参考）
SELECT 
    dept_id,
    dept_name,
    parent_id,
    ancestors,
    org_type
FROM sys_dept 
WHERE org_type = '2' AND del_flag = '0'
ORDER BY dept_name;
