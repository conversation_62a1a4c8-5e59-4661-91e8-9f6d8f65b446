-- 测试组织架构树数据权限控制脚本
-- 用于验证 /system/organization/tree 接口的权限过滤是否正常工作

-- 1. 查看测试用户的权限信息
SELECT 
    u.user_id,
    u.user_name,
    u.nick_name,
    u.dept_id,
    u.org_id,
    d.dept_name as org_name,
    d.org_type,
    CASE d.org_type 
        WHEN '0' THEN '部门'
        WHEN '1' THEN '单位'
        WHEN '2' THEN '企业'
        ELSE '未知'
    END as org_type_name,
    r.role_name,
    r.data_scope,
    CASE r.data_scope 
        WHEN '1' THEN '所有数据权限'
        WHEN '2' THEN '自定义数据权限'
        WHEN '3' THEN '本单位数据权限+企业数据权限'
        WHEN '4' THEN '本单位及以下数据权限+企业数据权限'
        WHEN '5' THEN '仅本人数据权限'
        WHEN '6' THEN '本企业数据权限'
        ELSE '未知'
    END as data_scope_name
FROM sys_user u
LEFT JOIN sys_dept d ON u.org_id = d.dept_id
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.del_flag = '0' AND r.del_flag = '0'
ORDER BY u.user_name, r.role_sort;

-- 2. 模拟不同权限级别的数据过滤效果

-- 2.1 全部数据权限 (data_scope = '1') - 应该能看到所有组织架构
-- 不会添加任何过滤条件

-- 2.2 本单位数据权限+企业数据权限 (data_scope = '3')
-- 假设用户org_id = 100，生成的SQL条件应该是：
-- AND (d.dept_id = 100 OR d.dept_id IN (SELECT dept_id FROM sys_dept WHERE org_type = '2'))

-- 测试查询：查看单位100的数据 + 所有企业数据
SELECT 
    d.dept_id,
    d.dept_name,
    d.org_type,
    CASE d.org_type 
        WHEN '0' THEN '部门'
        WHEN '1' THEN '单位'
        WHEN '2' THEN '企业'
        ELSE '未知'
    END as org_type_name
FROM sys_dept d
WHERE d.del_flag = '0' AND d.status = '0'
AND (d.dept_id = 100 OR d.dept_id IN (SELECT dept_id FROM sys_dept WHERE org_type = '2'))
ORDER BY d.org_type, d.order_num;

-- 2.3 本企业数据权限 (data_scope = '6')
-- 假设用户org_id = 234（企业），生成的SQL条件应该是：
-- AND (d.dept_id = 234 AND EXISTS (SELECT 1 FROM sys_dept WHERE dept_id = 234 AND org_type = '2'))

-- 测试查询：只能看企业234的数据
SELECT 
    d.dept_id,
    d.dept_name,
    d.org_type,
    CASE d.org_type 
        WHEN '0' THEN '部门'
        WHEN '1' THEN '单位'
        WHEN '2' THEN '企业'
        ELSE '未知'
    END as org_type_name
FROM sys_dept d
WHERE d.del_flag = '0' AND d.status = '0'
AND (d.dept_id = 234 AND EXISTS (SELECT 1 FROM sys_dept WHERE dept_id = 234 AND org_type = '2'))
ORDER BY d.org_type, d.order_num;

-- 3. 测试用户数据的权限过滤

-- 3.1 测试企业用户的权限过滤
-- 假设用户org_id = 234（企业），应该只能看到该企业的用户
SELECT 
    u.user_id,
    u.user_name,
    u.nick_name,
    u.dept_id,
    u.org_id,
    d.dept_name as org_name,
    d.org_type
FROM sys_user u
LEFT JOIN sys_dept d ON (u.dept_id = d.dept_id OR (u.dept_id IS NULL AND u.org_id = d.dept_id))
WHERE u.del_flag = '0' AND u.status = '0'
AND (d.del_flag = '0' OR d.del_flag IS NULL)
AND (d.status = '0' OR d.status IS NULL)
-- 模拟企业权限过滤条件
AND (d.dept_id = 234 AND EXISTS (SELECT 1 FROM sys_dept WHERE dept_id = 234 AND org_type = '2'))
ORDER BY u.user_name;

-- 4. 验证完整的组织架构查询权限过滤
-- 模拟 selectOrganizationTree 的完整查询，包含权限过滤

-- 4.1 部门/单位/企业数据（带权限过滤）
SELECT 
    'dept' as data_type,
    CONCAT(
        CASE
            WHEN d.org_type = '1' THEN 'unit_'
            WHEN d.org_type = '2' THEN 'enterprise_'
            ELSE 'dept_'
        END,
        d.dept_id
    ) as id,
    d.dept_id,
    d.dept_name as name,
    CASE
        WHEN d.org_type = '1' THEN 'unit'
        WHEN d.org_type = '2' THEN 'enterprise'
        ELSE 'dept'
    END as type
FROM sys_dept d
WHERE d.del_flag = '0' AND d.status = '0'
-- 模拟企业权限过滤（假设用户org_id = 234）
AND (d.dept_id = 234 AND EXISTS (SELECT 1 FROM sys_dept WHERE dept_id = 234 AND org_type = '2'))

UNION ALL

-- 4.2 用户数据（带权限过滤）
SELECT 
    'user' as data_type,
    CONCAT('user_', u.user_id) as id,
    COALESCE(u.dept_id, u.org_id) as dept_id,
    u.nick_name as name,
    'user' as type
FROM sys_user u
LEFT JOIN sys_dept d ON (u.dept_id = d.dept_id OR (u.dept_id IS NULL AND u.org_id = d.dept_id))
WHERE u.del_flag = '0' AND u.status = '0'
AND (d.del_flag = '0' OR d.del_flag IS NULL)
AND (d.status = '0' OR d.status IS NULL)
-- 模拟企业权限过滤（假设用户org_id = 234）
AND (d.dept_id = 234 AND EXISTS (SELECT 1 FROM sys_dept WHERE dept_id = 234 AND org_type = '2'))

ORDER BY data_type, type, name;
