-- 测试组织架构树中用户查询的SQL
-- 用于验证修改后的查询逻辑是否能正确查询到企业中的用户

-- 1. 查看测试数据
SELECT 
    u.user_id,
    u.user_name,
    u.nick_name,
    u.dept_id,
    u.org_id,
    d.dept_id as dept_table_id,
    d.dept_name,
    d.org_type,
    CASE d.org_type 
        WHEN '0' THEN '部门'
        WHEN '1' THEN '单位'
        WHEN '2' THEN '企业'
        ELSE '未知'
    END as org_type_name
FROM sys_user u
LEFT JOIN sys_dept d ON (u.dept_id = d.dept_id OR (u.dept_id IS NULL AND u.org_id = d.dept_id))
WHERE u.user_name = 'lszTest';

-- 2. 测试修改后的用户查询逻辑（模拟 OrganizationTreeMapper 中的查询）
SELECT
    CONCAT('user_', u.user_id) as id,
    COALESCE(u.dept_id, u.org_id) as dept_id,
    up.post_id,
    u.user_id,
    u.org_id,
    u.nick_name as name,
    'user' as type,
    CASE
        WHEN up.post_id IS NOT NULL AND u.dept_id IS NOT NULL THEN CONCAT('post_', up.post_id, '_dept_', u.dept_id)
        WHEN up.post_id IS NOT NULL AND u.dept_id IS NULL THEN CONCAT('post_', up.post_id, '_dept_', u.org_id)
        WHEN u.dept_id IS NOT NULL THEN CONCAT(
            CASE
                WHEN d.org_type = '1' THEN 'unit_'
                WHEN d.org_type = '2' THEN 'enterprise_'
                ELSE 'dept_'
            END,
            u.dept_id
        )
        ELSE CONCAT(
            CASE
                WHEN d.org_type = '1' THEN 'unit_'
                WHEN d.org_type = '2' THEN 'enterprise_'
                ELSE 'dept_'
            END,
            u.org_id
        )
    END as parent_id,
    u.phonenumber as phone,
    u.email,
    NULL as leader,
    u.nick_name,
    u.user_name,
    NULL as post_code,
    -- 获取单位名称
    CASE
        WHEN d.org_type = '1' THEN d.dept_name
        WHEN d.org_type = '2' THEN d.dept_name
        ELSE (
            SELECT parent_dept.dept_name
            FROM sys_dept parent_dept
            WHERE parent_dept.org_type IN ('1', '2')
            AND FIND_IN_SET(CAST(parent_dept.dept_id AS CHAR), d.ancestors)
            ORDER BY parent_dept.dept_id DESC
            LIMIT 1
        )
    END as unit_name,
    -- 部门名称
    CASE
        WHEN d.org_type = '0' THEN d.dept_name
        ELSE NULL
    END as dept_name,
    -- 岗位名称
    p.post_name as post_name,
    u.status,
    NULL as order_num
FROM sys_user u
LEFT JOIN sys_user_post up ON u.user_id = up.user_id
LEFT JOIN sys_post p ON up.post_id = p.post_id
LEFT JOIN sys_dept d ON (u.dept_id = d.dept_id OR (u.dept_id IS NULL AND u.org_id = d.dept_id))
WHERE u.del_flag = '0'
AND u.status = '0'
AND (d.del_flag = '0' OR d.del_flag IS NULL)
AND (d.status = '0' OR d.status IS NULL)
AND u.user_name = 'lszTest';

-- 3. 查看企业234的层级结构
SELECT
    dept_id,
    parent_id,
    ancestors,
    dept_name,
    org_type,
    CASE org_type
        WHEN '0' THEN '部门'
        WHEN '1' THEN '单位'
        WHEN '2' THEN '企业'
        ELSE '未知'
    END as org_type_name
FROM sys_dept
WHERE dept_id IN (202, 234) OR FIND_IN_SET('202', ancestors) OR FIND_IN_SET('234', ancestors)
ORDER BY dept_id;

-- 4. 测试岗位查询（如果用户有岗位的话）
SELECT DISTINCT
    CONCAT('post_', p.post_id, '_dept_', COALESCE(u.dept_id, u.org_id)) as id,
    COALESCE(u.dept_id, u.org_id) as dept_id,
    p.post_id,
    NULL as user_id,
    NULL as org_id,
    p.post_name as name,
    'post' as type,
    CONCAT(
        CASE
            WHEN d.org_type = '1' THEN 'unit_'
            WHEN d.org_type = '2' THEN 'enterprise_'
            ELSE 'dept_'
        END,
        COALESCE(u.dept_id, u.org_id)
    ) as parent_id,
    u.user_name as related_user
FROM sys_post p
INNER JOIN sys_user_post up ON p.post_id = up.post_id
INNER JOIN sys_user u ON up.user_id = u.user_id
LEFT JOIN sys_dept d ON (u.dept_id = d.dept_id OR (u.dept_id IS NULL AND u.org_id = d.dept_id))
WHERE p.status = '0'
AND u.del_flag = '0'
AND u.status = '0'
AND (d.del_flag = '0' OR d.del_flag IS NULL)
AND (d.status = '0' OR d.status IS NULL)
AND u.user_name = 'lszTest';

-- 5. 完整的组织架构查询测试（模拟完整的 selectOrganizationTree 查询）
-- 只查询企业234相关的数据
SELECT * FROM (
    -- 部门/单位/企业数据
    SELECT
        CONCAT(
            CASE
                WHEN d.org_type = '1' THEN 'unit_'
                WHEN d.org_type = '2' THEN 'enterprise_'
                ELSE 'dept_'
            END,
            d.dept_id
        ) as id,
        d.dept_id,
        NULL as post_id,
        NULL as user_id,
        NULL as org_id,
        d.dept_name as name,
        CASE
            WHEN d.org_type = '1' THEN 'unit'
            WHEN d.org_type = '2' THEN 'enterprise'
            ELSE 'dept'
        END as type,
        CASE
            WHEN d.parent_id = 0 THEN NULL
            ELSE CONCAT(
                CASE
                    WHEN (SELECT org_type FROM sys_dept WHERE dept_id = d.parent_id) = '1' THEN 'unit_'
                    WHEN (SELECT org_type FROM sys_dept WHERE dept_id = d.parent_id) = '2' THEN 'enterprise_'
                    ELSE 'dept_'
                END,
                d.parent_id
            )
        END as parent_id,
        d.order_num,
        1 as query_order
    FROM sys_dept d
    WHERE d.del_flag = '0' AND d.status = '0'
    AND (d.dept_id IN (202, 234) OR FIND_IN_SET('202', d.ancestors) OR FIND_IN_SET('234', d.ancestors))

    UNION ALL

    -- 用户数据
    SELECT
        CONCAT('user_', u.user_id) as id,
        COALESCE(u.dept_id, u.org_id) as dept_id,
        up.post_id,
        u.user_id,
        u.org_id,
        u.nick_name as name,
        'user' as type,
        CASE
            WHEN up.post_id IS NOT NULL AND u.dept_id IS NOT NULL THEN CONCAT('post_', up.post_id, '_dept_', u.dept_id)
            WHEN up.post_id IS NOT NULL AND u.dept_id IS NULL THEN CONCAT('post_', up.post_id, '_dept_', u.org_id)
            WHEN u.dept_id IS NOT NULL THEN CONCAT(
                CASE
                    WHEN d.org_type = '1' THEN 'unit_'
                    WHEN d.org_type = '2' THEN 'enterprise_'
                    ELSE 'dept_'
                END,
                u.dept_id
            )
            ELSE CONCAT(
                CASE
                    WHEN d.org_type = '1' THEN 'unit_'
                    WHEN d.org_type = '2' THEN 'enterprise_'
                    ELSE 'dept_'
                END,
                u.org_id
            )
        END as parent_id,
        NULL as order_num,
        2 as query_order
    FROM sys_user u
    LEFT JOIN sys_user_post up ON u.user_id = up.user_id
    LEFT JOIN sys_dept d ON (u.dept_id = d.dept_id OR (u.dept_id IS NULL AND u.org_id = d.dept_id))
    WHERE u.del_flag = '0'
    AND u.status = '0'
    AND (d.del_flag = '0' OR d.del_flag IS NULL)
    AND (d.status = '0' OR d.status IS NULL)
    AND u.user_name = 'lszTest'
) combined_result
ORDER BY query_order, type, order_num, name;
