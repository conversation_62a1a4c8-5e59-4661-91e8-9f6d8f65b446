-- 更新数据权限字典数据
-- 添加新的"本企业数据权限"选项

-- 查看当前的数据权限字典数据
SELECT * FROM sys_dict_data WHERE dict_type = 'sys_role_data_scope' ORDER BY dict_sort;

-- 插入新的字典数据（如果不存在的话）
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark)
SELECT 6, '本企业数据权限', '6', 'sys_role_data_scope', '', 'default', 'N', '0', 'admin', NOW(), '本企业数据权限'
WHERE NOT EXISTS (
    SELECT 1 FROM sys_dict_data 
    WHERE dict_type = 'sys_role_data_scope' AND dict_value = '6'
);

-- 验证插入结果
SELECT * FROM sys_dict_data WHERE dict_type = 'sys_role_data_scope' ORDER BY dict_sort;
