package com.tocc.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 外部系统API接口配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "external.api")
public class ExternalApiProperties 
{
    /**
     * 系统管理相关接口
     */
    private System system = new System();
    
    /**
     * 交通数据相关接口
     */
    private Traffic traffic = new Traffic();
    
    /**
     * 监控数据相关接口
     */
    private Monitor monitor = new Monitor();
    
    /**
     * 报表数据相关接口
     */
    private Report report = new Report();
    
    /**
     * 自定义接口配置
     */
    private Map<String, ApiConfig> custom;

    public System getSystem()
    {
        return system;
    }

    public void setSystem(System system)
    {
        this.system = system;
    }

    public Traffic getTraffic()
    {
        return traffic;
    }

    public void setTraffic(Traffic traffic)
    {
        this.traffic = traffic;
    }

    public Monitor getMonitor()
    {
        return monitor;
    }

    public void setMonitor(Monitor monitor)
    {
        this.monitor = monitor;
    }

    public Report getReport()
    {
        return report;
    }

    public void setReport(Report report)
    {
        this.report = report;
    }

    public Map<String, ApiConfig> getCustom()
    {
        return custom;
    }

    public void setCustom(Map<String, ApiConfig> custom)
    {
        this.custom = custom;
    }
    
    /**
     * 系统管理接口配置
     */
    public static class System
    {
        private String userList = "/system/user/list";
        private String userDetail = "/system/user/{id}";
        private String userCreate = "/system/user";
        private String userUpdate = "/system/user";
        private String userDelete = "/system/user/{id}";
        private String roleList = "/system/role/list";
        private String deptList = "/system/dept/list";
        private String menuList = "/system/menu/list";
        private String dictList = "/system/dict/data/list";

        // getters and setters
        public String getUserList() { return userList; }
        public void setUserList(String userList) { this.userList = userList; }

        public String getUserDetail() { return userDetail; }
        public void setUserDetail(String userDetail) { this.userDetail = userDetail; }

        public String getUserCreate() { return userCreate; }
        public void setUserCreate(String userCreate) { this.userCreate = userCreate; }

        public String getUserUpdate() { return userUpdate; }
        public void setUserUpdate(String userUpdate) { this.userUpdate = userUpdate; }

        public String getUserDelete() { return userDelete; }
        public void setUserDelete(String userDelete) { this.userDelete = userDelete; }

        public String getRoleList() { return roleList; }
        public void setRoleList(String roleList) { this.roleList = roleList; }

        public String getDeptList() { return deptList; }
        public void setDeptList(String deptList) { this.deptList = deptList; }

        public String getMenuList() { return menuList; }
        public void setMenuList(String menuList) { this.menuList = menuList; }

        public String getDictList() { return dictList; }
        public void setDictList(String dictList) { this.dictList = dictList; }
    }

    /**
     * 交通数据接口配置
     */
    public static class Traffic
    {
        private String stationList = "/traffic/station/list";
        private String stationDetail = "/traffic/station/{id}";
        private String stationPreOneHour = "admin-api/charge/statApi/Api230724/stationPreOneHour";
        private String stationFlowData = "/traffic/station/flow";
        private String roadSectionList = "/traffic/road/list";
        private String roadSectionDetail = "/traffic/road/{id}";
        private String trafficIncident = "/traffic/incident/list";
        private String weatherData = "/traffic/weather/current";
        private String sectionPreOneHour = "charge/roadApi/Api230724/sectionPreOneHour";
        private String getFlowServiceNew = "infra/serviceArea/getFlowServiceNew";
        private String interFlowPreOneHour = "charge/inter/Api230724/interFlowPreOneHour";
        private String stationFlowDetail = "charge/api-v2/stationFlowDetail";
        private String dataHighwayLength = "system/dict-data/page?dictType=data_highway_length&pageSize=100&pageNo=1";
        private String getScreenLeftData = "charge/common/getScreenLeftData";

        // getters and setters
        public String getStationList() { return stationList; }
        public void setStationList(String stationList) { this.stationList = stationList; }

        public String getStationDetail() { return stationDetail; }
        public void setStationDetail(String stationDetail) { this.stationDetail = stationDetail; }

        public String getStationPreOneHour() { return stationPreOneHour; }
        public void setStationPreOneHour(String stationPreOneHour) { this.stationPreOneHour = stationPreOneHour; }

        public String getStationFlowData() { return stationFlowData; }
        public void setStationFlowData(String stationFlowData) { this.stationFlowData = stationFlowData; }

        public String getRoadSectionList() { return roadSectionList; }
        public void setRoadSectionList(String roadSectionList) { this.roadSectionList = roadSectionList; }

        public String getRoadSectionDetail() { return roadSectionDetail; }
        public void setRoadSectionDetail(String roadSectionDetail) { this.roadSectionDetail = roadSectionDetail; }

        public String getTrafficIncident() { return trafficIncident; }
        public void setTrafficIncident(String trafficIncident) { this.trafficIncident = trafficIncident; }

        public String getWeatherData() { return weatherData; }
        public void setWeatherData(String weatherData) { this.weatherData = weatherData; }

        public String getSectionPreOneHour() { return sectionPreOneHour; }
        public void setSectionPreOneHour(String sectionPreOneHour) { this.sectionPreOneHour = sectionPreOneHour; }

        public String getGetFlowServiceNew() { return getFlowServiceNew; }
        public void setGetFlowServiceNew(String getFlowServiceNew) { this.getFlowServiceNew = getFlowServiceNew; }

        public String getInterFlowPreOneHour() { return interFlowPreOneHour; }
        public void setInterFlowPreOneHour(String interFlowPreOneHour) { this.interFlowPreOneHour = interFlowPreOneHour; }

        public String getStationFlowDetail() { return stationFlowDetail; }
        public void setStationFlowDetail(String stationFlowDetail) { this.stationFlowDetail = stationFlowDetail; }

        public String getDataHighwayLength() { return dataHighwayLength; }
        public void setDataHighwayLength(String dataHighwayLength) { this.dataHighwayLength = dataHighwayLength; }

        public String getGetScreenLeftData() { return getScreenLeftData; }
        public void setGetScreenLeftData(String getScreenLeftData) { this.getScreenLeftData = getScreenLeftData; }
    }
    
    /**
     * 监控数据接口配置
     */
    public static class Monitor
    {
        private String systemStatus = "/monitor/system/status";
        private String serverInfo = "/monitor/server/info";
        private String operationLog = "/monitor/operation/log";
        private String loginLog = "/monitor/login/log";
        private String performanceData = "/monitor/performance/data";

        // getters and setters
        public String getSystemStatus() { return systemStatus; }
        public void setSystemStatus(String systemStatus) { this.systemStatus = systemStatus; }

        public String getServerInfo() { return serverInfo; }
        public void setServerInfo(String serverInfo) { this.serverInfo = serverInfo; }

        public String getOperationLog() { return operationLog; }
        public void setOperationLog(String operationLog) { this.operationLog = operationLog; }

        public String getLoginLog() { return loginLog; }
        public void setLoginLog(String loginLog) { this.loginLog = loginLog; }

        public String getPerformanceData() { return performanceData; }
        public void setPerformanceData(String performanceData) { this.performanceData = performanceData; }
    }

    /**
     * 报表数据接口配置
     */
    public static class Report
    {
        private String trafficReport = "/report/traffic/summary";
        private String statisticsReport = "/report/statistics/data";
        private String exportData = "/report/export/{type}";
        private String chartData = "/report/chart/data";

        // getters and setters
        public String getTrafficReport() { return trafficReport; }
        public void setTrafficReport(String trafficReport) { this.trafficReport = trafficReport; }

        public String getStatisticsReport() { return statisticsReport; }
        public void setStatisticsReport(String statisticsReport) { this.statisticsReport = statisticsReport; }

        public String getExportData() { return exportData; }
        public void setExportData(String exportData) { this.exportData = exportData; }

        public String getChartData() { return chartData; }
        public void setChartData(String chartData) { this.chartData = chartData; }
    }

    /**
     * API配置项
     */
    public static class ApiConfig
    {
        private String endpoint;
        private String method = "GET";
        private String description;
        private boolean requireAuth = true;
        private boolean cacheable = false;
        private int cacheTime = 300; // 缓存时间（秒）
        private Map<String, String> defaultHeaders;

        public ApiConfig() {}

        public ApiConfig(String endpoint, String method, String description)
        {
            this.endpoint = endpoint;
            this.method = method;
            this.description = description;
        }

        // getters and setters
        public String getEndpoint() { return endpoint; }
        public void setEndpoint(String endpoint) { this.endpoint = endpoint; }

        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public boolean isRequireAuth() { return requireAuth; }
        public void setRequireAuth(boolean requireAuth) { this.requireAuth = requireAuth; }

        public boolean isCacheable() { return cacheable; }
        public void setCacheable(boolean cacheable) { this.cacheable = cacheable; }

        public int getCacheTime() { return cacheTime; }
        public void setCacheTime(int cacheTime) { this.cacheTime = cacheTime; }

        public Map<String, String> getDefaultHeaders() { return defaultHeaders; }
        public void setDefaultHeaders(Map<String, String> defaultHeaders) { this.defaultHeaders = defaultHeaders; }
    }
} 