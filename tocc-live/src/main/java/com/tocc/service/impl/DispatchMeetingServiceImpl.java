package com.tocc.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.common.core.domain.entity.SysDept;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.config.AgoraConfig;
import com.tocc.domain.dto.RescueTeamDTO;
import com.tocc.domain.dto.WarehouseDTO;
import com.tocc.domain.qo.AddressBookQo;
import com.tocc.domain.vo.AddressBookVO;
import com.tocc.domain.vo.AddressBookGroupVO;
import com.tocc.domain.vo.RescueTeamVO;
import com.tocc.domain.vo.WarehouseVO;
import com.tocc.em.domain.EmPrePlanDept;
import com.tocc.em.domain.EmPrePlanDeptUser;
import com.tocc.em.service.IEmPrePlanDeptService;
import com.tocc.em.service.IEmPrePlanDeptUserService;
import com.tocc.service.IRescueTeamService;
import com.tocc.service.IWarehouseService;
import com.tocc.system.domain.SysPost;
import com.tocc.system.domain.dto.ExpertInfoDTO;
import com.tocc.system.domain.vo.ExpertInfoVO;
import com.tocc.system.service.IExpertService;
import com.tocc.system.service.ISysDeptService;
import com.tocc.system.service.ISysPostService;
import io.agora.media.RtcTokenBuilder2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.mapper.DispatchMeetingMapper;
import com.tocc.domain.DispatchMeeting;
import com.tocc.service.IDispatchMeetingService;

import javax.annotation.Resource;

/**
 * 现场指挥协调会议Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
public class DispatchMeetingServiceImpl extends ServiceImpl<DispatchMeetingMapper,DispatchMeeting>implements IDispatchMeetingService {

    @Autowired
    private DispatchMeetingMapper dispatchMeetingMapper;

    @Resource
    private AgoraConfig agoraConfig;

    @Resource
    private IEmPrePlanDeptUserService emPrePlanDeptUserService;

    @Resource
    private IExpertService expertService;

    @Resource
    private IRescueTeamService rescueTeamService;

    @Resource
    private IWarehouseService warehouseService;

    @Resource
    private ISysPostService postService;

    @Resource
    private IEmPrePlanDeptService emPrePlanDeptService;

    @Resource
    private ISysDeptService sysDeptService;

    private final RtcTokenBuilder2 tokenBuilder = new RtcTokenBuilder2();

    /**
     * 查询现场指挥协调会议
     *
     * @param id 现场指挥协调会议主键
     * @return 现场指挥协调会议
     */
    @Override
    public DispatchMeeting selectDispatchMeetingById(Long id) {
        return dispatchMeetingMapper.selectDispatchMeetingById(id);
    }

    /**
     * 查询现场指挥协调会议列表
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 现场指挥协调会议
     */
    @Override
    public List<DispatchMeeting> selectDispatchMeetingList(DispatchMeeting dispatchMeeting) {
        return dispatchMeetingMapper.selectDispatchMeetingList(dispatchMeeting);
    }

    /**
     * 新增现场指挥协调会议
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 结果
     */
    @Override
    public Map<String, Object> insertDispatchMeeting(DispatchMeeting dispatchMeeting) {
        if (ObjectUtil.isNull(dispatchMeeting)) {
            throw new ServiceException("频道不能为空");
        }
        //设置channelName
        String channelName = IdUtil.fastSimpleUUID();
        dispatchMeeting.setChannelName(channelName);
//        dispatchMeeting.setCreateTime(DateUtils.getNowDate());
        boolean result = this.save(dispatchMeeting);
//        int result = dispatchMeetingMapper.insertDispatchMeeting(dispatchMeeting);
        String token = null;
        if (result) {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (ObjectUtil.isNull(loginUser)) {
                throw new ServiceException("用户未登录,无法加入会商");
            }
            Long userId = loginUser.getUser().getUserId();
            String userIdStr = String.valueOf(userId);
            token = tokenBuilder.buildTokenWithUserAccount(agoraConfig.getAppId(), agoraConfig.getAppCertificate(),
                    dispatchMeeting.getChannelName(), userIdStr, RtcTokenBuilder2.Role.ROLE_PUBLISHER,
                    agoraConfig.getTokenExpiration(),
                    agoraConfig.getPrivilegeExpiration());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("meetingId",dispatchMeeting.getId());
        map.put("token", token);
        map.put("channelName", channelName);
        return map;
    }

    /**
     * 修改现场指挥协调会议
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 结果
     */
    @Override
    public int updateDispatchMeeting(DispatchMeeting dispatchMeeting) {
        dispatchMeeting.setUpdateTime(DateUtils.getNowDate());
        return dispatchMeetingMapper.updateDispatchMeeting(dispatchMeeting);
    }

    /**
     * 批量删除现场指挥协调会议
     *
     * @param ids 需要删除的现场指挥协调会议主键
     * @return 结果
     */
    @Override
    public int deleteDispatchMeetingByIds(Long[] ids) {
        return dispatchMeetingMapper.deleteDispatchMeetingByIds(ids);
    }

    /**
     * 删除现场指挥协调会议信息
     *
     * @param id 现场指挥协调会议主键
     * @return 结果
     */
    @Override
    public int deleteDispatchMeetingById(Long id) {
        return dispatchMeetingMapper.deleteDispatchMeetingById(id);
    }

    @Override
    public String generateTokenWithAccount(Long meetingId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new ServiceException("用户未登录,无法加入会商");
        }
        if (ObjectUtil.isNull(meetingId)) {
            throw new ServiceException("会议id不能为空");
        }
        DispatchMeeting dispatchMeeting = dispatchMeetingMapper.selectDispatchMeetingById(meetingId);
        if (ObjectUtil.isNull(dispatchMeeting)) {
            throw new ServiceException("会议不存在");
        }
        Long userId = loginUser.getUser().getUserId();
        String userIdStr = String.valueOf(userId);
        String token = tokenBuilder.buildTokenWithUserAccount(agoraConfig.getAppId(), agoraConfig.getAppCertificate(),
                dispatchMeeting.getChannelName(), userIdStr, RtcTokenBuilder2.Role.ROLE_PUBLISHER,
                agoraConfig.getTokenExpiration(),
                agoraConfig.getPrivilegeExpiration());
        return token;
    }

    @Override
    public DispatchMeeting getMeetingInfoByCode(String code) {
        if (StrUtil.isEmpty(code)) {
            throw new ServiceException("会议码不能为空");
        }
        DispatchMeeting meetingInfo = dispatchMeetingMapper.getMeetingInfoByCode(code);
        return meetingInfo;
    }

    @Override
    public List<AddressBookGroupVO> addressBook(AddressBookQo addressBookQo) {
        List<AddressBookVO> addressBookVOList = new ArrayList<>();

        ExpertInfoDTO expertInfoDTO = new ExpertInfoDTO();
        EmPrePlanDeptUser emPrePlanDeptUser = new EmPrePlanDeptUser();
        RescueTeamDTO rescueTeamDTO = new RescueTeamDTO();
        WarehouseDTO warehouseDTO = new WarehouseDTO();
        
        // 参数校验
        if (addressBookQo == null) {
            return new ArrayList<>();
        }
        
        String contacts = null;
        if (addressBookQo.getContacts() != null && !addressBookQo.getContacts().trim().isEmpty()) {
            contacts = addressBookQo.getContacts().trim();
            expertInfoDTO.setName(contacts);
            emPrePlanDeptUser.setLeaderName(contacts);
            rescueTeamDTO.setLeaderName(contacts);
            warehouseDTO.setPrincipal(contacts);
        }

        //获取所有职位
        List<SysPost> posts = postService.selectPostAll();
        //获取所有系统单位 sys_dept
        List<SysDept> sysDepts = sysDeptService.selectDepartmentList(new SysDept());
        //获取预案组织机构 em_pre_plan_dept
        List<EmPrePlanDept> emPrePlanDepts = emPrePlanDeptService.selectEmPrePlanDeptList(new EmPrePlanDept());

        // 1. 查询应急预案组织机构人员
        List<EmPrePlanDeptUser> emPrePlanDeptUsers = emPrePlanDeptUserService.selectEmPrePlanDeptUserList(emPrePlanDeptUser);
        if (emPrePlanDeptUsers != null) {
            for (EmPrePlanDeptUser user : emPrePlanDeptUsers) {
                AddressBookVO vo = new AddressBookVO();
                vo.setGroupName(getEmDeptName(user.getEmDeptId(), emPrePlanDepts));
                vo.setUserName(user.getLeaderName());
                vo.setDeptName(getDeptName(user.getDepId(), sysDepts));
                vo.setPost(getPostNames(user.getPost(), posts));
                vo.setContactNumber(user.getContact());
                addressBookVOList.add(vo);
            }
        }
        
        // 2. 查询专家
        List<ExpertInfoVO> expertInfoVOS = expertService.selectExpertInfoList(expertInfoDTO);
        if (expertInfoVOS != null) {
            for (ExpertInfoVO expert : expertInfoVOS) {
                AddressBookVO vo = new AddressBookVO();
                vo.setGroupName("专家库");
                vo.setUserName(expert.getName());
                vo.setDeptName(expert.getWorkUnit());
                vo.setPost(expert.getSpecialtyField());
                vo.setContactNumber(expert.getPhone());
                addressBookVOList.add(vo);
            }
        }
        
        // 3. 查询救援队伍
        List<RescueTeamVO> allTeams = rescueTeamService.selectRescueTeamList(rescueTeamDTO);
        if (allTeams != null) {
            for (RescueTeamVO team : allTeams) {
                AddressBookVO vo = new AddressBookVO();
                vo.setGroupName("救援队伍");
                vo.setUserName(team.getLeaderName() != null ? team.getLeaderName() : team.getTeamName());
                vo.setDeptName(team.getTeamName());
                vo.setPost(team.getTeamTypeName());
                vo.setContactNumber(team.getLeaderPhone());
                addressBookVOList.add(vo);
            }
        }
        
        // 4. 查询应急物资仓库管理员
        List<WarehouseVO> allWarehouses = warehouseService.selectWarehouseList(warehouseDTO);
        if (allWarehouses != null) {
            for (WarehouseVO warehouse : allWarehouses) {
                AddressBookVO vo = new AddressBookVO();
                vo.setGroupName("应急物资");
                vo.setUserName(warehouse.getPrincipal());
                vo.setDeptName(warehouse.getBelongOrgName());
                vo.setPost("仓库负责人");
                vo.setContactNumber(warehouse.getContactPhone());
                addressBookVOList.add(vo);
            }
        }
        
        // TODO: 医疗单位联系人查询 - 待实现
        // TODO: 消防单位联系人查询 - 待实现

        // 根据 groupName 进行分组
        return groupByGroupName(addressBookVOList);
    }

    /**
     * 根据groupName对联系人进行分组
     */
    private List<AddressBookGroupVO> groupByGroupName(List<AddressBookVO> addressBookVOList) {
        if (addressBookVOList == null || addressBookVOList.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用Stream API按groupName分组
        Map<String, List<AddressBookVO>> groupedMap = addressBookVOList.stream()
                .filter(vo -> vo.getGroupName() != null && !vo.getGroupName().trim().isEmpty())
                .collect(Collectors.groupingBy(AddressBookVO::getGroupName));

        // 转换为AddressBookGroupVO列表
        List<AddressBookGroupVO> result = new ArrayList<>();
        for (Map.Entry<String, List<AddressBookVO>> entry : groupedMap.entrySet()) {
            String groupName = entry.getKey();
            List<AddressBookVO> contacts = entry.getValue();
            
            // 对每个分组内的联系人按姓名排序
            contacts.sort((a, b) -> {
                String nameA = a.getUserName() != null ? a.getUserName() : "";
                String nameB = b.getUserName() != null ? b.getUserName() : "";
                return nameA.compareTo(nameB);
            });
            
            AddressBookGroupVO groupVO = new AddressBookGroupVO(groupName, contacts);
            result.add(groupVO);
        }

        // 对分组按groupName排序
        result.sort((a, b) -> {
            String nameA = a.getGroup() != null ? a.getGroup() : "";
            String nameB = b.getGroup() != null ? b.getGroup() : "";
            return nameA.compareTo(nameB);
        });

        return result;
    }

    /**
     * 根据职位ID获取职位名称
     */
    private String getPostNames(String postIds, List<SysPost> posts) {
        if (postIds == null || postIds.trim().isEmpty() || posts == null) {
            return "";
        }
        
        try {
            return Arrays.stream(postIds.split(","))
                    .filter(postId -> postId != null && !postId.trim().isEmpty())
                    .map(postId -> {
                        try {
                            Long id = Long.valueOf(postId.trim());
                            return posts.stream()
                                    .filter(post -> post.getPostId() != null && post.getPostId().equals(id))
                                    .findFirst()
                                    .map(SysPost::getPostName)
                                    .orElse("");
                        } catch (NumberFormatException e) {
                            return "";
                        }
                    })
                    .filter(name -> !name.isEmpty())
                    .collect(Collectors.joining(","));
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 根据应急部门ID获取部门名称
     */
    private String getEmDeptName(String emDeptId, List<EmPrePlanDept> emPrePlanDepts) {
        if (emDeptId == null || emDeptId.trim().isEmpty() || emPrePlanDepts == null) {
            return "";
        }
        
        return emPrePlanDepts.stream()
                .filter(dept -> dept.getId() != null && dept.getId().equals(emDeptId))
                .findFirst()
                .map(EmPrePlanDept::getDeptName)
                .orElse("");
    }

    /**
     * 根据系统部门ID获取部门名称
     */
    private String getDeptName(String deptId, List<SysDept> sysDepts) {
        if (deptId == null || deptId.trim().isEmpty() || sysDepts == null) {
            return "";
        }
        
        try {
            Long id = Long.valueOf(deptId);
            return sysDepts.stream()
                    .filter(dept -> dept.getDeptId() != null && dept.getDeptId().equals(id))
                    .findFirst()
                    .map(SysDept::getDeptName)
                    .orElse("");
        } catch (NumberFormatException e) {
            return "";
        }
    }
}
