package com.tocc.risk.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 项目对象 risk_projects
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@ApiModel(value = "在建项目对象")
public class Projects extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    private String id;

    /** 驻地名称 */
    @ApiModelProperty(value = "驻地名称")
    @Excel(name = "驻地名称")
    private String residentName;

    /** 驻地类型（字典表resident_type） */
    @ApiModelProperty(value = "驻地类型（字典表resident_type）")
    @Excel(name = "驻地类型", readConverterExp = "字=典表resident_type")
    private String residentType;

    /** 坐标点位 */
    @ApiModelProperty(value = "坐标点位")
    @Excel(name = "坐标点位")
    private String coordinate;

    /** 项目名称 */
    @ApiModelProperty(value = "项目名称")
    @Excel(name = "项目名称")
    private String projectName;

    /** 项目类型（字典project_type） */
    @ApiModelProperty(value = "项目类型（字典project_type）")
    @Excel(name = "项目类型", readConverterExp = "字=典project_type")
    private String projectType;

    /** 建设单位 */
    @ApiModelProperty(value = "建设单位")
    @Excel(name = "建设单位")
    private String buildUnit;

    /** 施工单位 */
    @ApiModelProperty(value = "施工单位")
    @Excel(name = "施工单位")
    private String constructionUnit;

    /** 驻地地址 */
    @ApiModelProperty(value = "驻地地址")
    @Excel(name = "驻地地址")
    private String address;

    /** 行政区域 */
    @ApiModelProperty(value = "行政区域")
    @Excel(name = "行政区域")
    private String area;

    /** 驻地人数 */
    @ApiModelProperty(value = "驻地人数")
    @Excel(name = "驻地人数")
    private Integer residentsNum;

    /** 风险等级（字典risk_level） */
    @ApiModelProperty(value = "风险等级（字典risk_level）")
    @Excel(name = "风险等级", readConverterExp = "字=典risk_level")
    private String riskLevel;

    /** 房建类型（字典room_type） */
    @ApiModelProperty(value = "房建类型（字典room_type）")
    @Excel(name = "房建类型", readConverterExp = "字=典room_type")
    private String roomType;

    /** 主管是否排查（1-是，0-否） */
    @ApiModelProperty(value = "主管是否排查（1-是，0-否）")
    @Excel(name = "主管是否排查", readConverterExp = "1=-是，0-否")
    private Integer headInv;

    /** 是否属于临水、临崖、涉洪区域（1-是，0-否） */
    @ApiModelProperty(value = "是否属于临水、临崖、涉洪区域（1-是，0-否）")
    @Excel(name = "是否属于临水、临崖、涉洪区域", readConverterExp = "1=-是，0-否")
    private Integer isCliff;

    /** 是否属于易垮塌区域（1-是，0-否） */
    @ApiModelProperty(value = "是否属于易垮塌区域（1-是，0-否）")
    @Excel(name = "是否属于易垮塌区域", readConverterExp = "1=-是，0-否")
    private Integer isCollapse;

    /** 是否搬迁（1-是，0-否） */
    @ApiModelProperty(value = "是否搬迁（1-是，0-否）")
    @Excel(name = "是否搬迁", readConverterExp = "1=-是，0-否")
    private Integer isRelocate;

    /** 项目开始时间 */
    @ApiModelProperty(value = "项目开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "项目开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 预计结束时间 */
    @ApiModelProperty(value = "预计结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预计结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 吹哨人 */
    @ApiModelProperty(value = "吹哨人")
    @Excel(name = "吹哨人")
    private String whistling;

    /** 吹哨人电话 */
    @ApiModelProperty(value = "吹哨人电话")
    @Excel(name = "吹哨人电话")
    private String whistlingTel;

    /** 建设单位包保责任人 */
    @ApiModelProperty(value = "建设单位包保责任人")
    @Excel(name = "建设单位包保责任人")
    private String builder;

    /** 建设单位包保责任人ID */
    @ApiModelProperty(value = "建设单位包保责任人ID")
    @Excel(name = "建设单位包保责任人ID")
    private Long builderId;

    /** 建设单位包保责任人电话 */
    @ApiModelProperty(value = "建设单位包保责任人电话")
    @Excel(name = "建设单位包保责任人电话")
    private String builderTel;

    /** 施工单位包保责任人 */
    @ApiModelProperty(value = "施工单位包保责任人")
    @Excel(name = "施工单位包保责任人")
    private String construc;

    /** 施工单位包保责任人电话 */
    @ApiModelProperty(value = "施工单位包保责任人电话")
    @Excel(name = "施工单位包保责任人电话")
    private String construcTel;

    /** 驻地现场包保责任人 */
    @ApiModelProperty(value = "驻地现场包保责任人")
    @Excel(name = "驻地现场包保责任人")
    private String addresser;

    /** 驻地现场包保责任人电话 */
    @ApiModelProperty(value = "驻地现场包保责任人电话")
    @Excel(name = "驻地现场包保责任人电话")
    private String addresserTel;

    /** 县级包保联系人 */
    @ApiModelProperty(value = "县级包保联系人")
    @Excel(name = "县级包保联系人")
    private String countyer;

    /** 县级包保联系人电话 */
    @ApiModelProperty(value = "县级包保联系人电话")
    @Excel(name = "县级包保联系人电话")
    private String countyerTel;

    /** 市级包保联系人 */
    @ApiModelProperty(value = "市级包保联系人")
    @Excel(name = "市级包保联系人")
    private String marketer;

    /** 市级包保联系人电话 */
    @ApiModelProperty(value = "市级包保联系人电话")
    @Excel(name = "市级包保联系人电话")
    private String marketerTel;

    /** 省级包保联系人 */
    @ApiModelProperty(value = "省级包保联系人")
    @Excel(name = "省级包保联系人")
    private String provincer;

    /** 省级包保联系人电话 */
    @ApiModelProperty(value = "省级包保联系人电话")
    @Excel(name = "省级包保联系人电话")
    private String provincerTel;

    /** 状态（0-未开工，1-已完工，2-已暂停） */
    @ApiModelProperty(value = "状态（0-未开工，1-已完工，2-已暂停）")
    @Excel(name = "状态", readConverterExp = "0=-未开工，1-已完工，2-已暂停")
    private Integer status;

    /** 创建人ID */
    @ApiModelProperty(value = "创建人ID")
    @Excel(name = "创建人ID")
    private Long createById;

    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "是否删除（0-否，1-是）")
    private Integer delFlag;

    /** 项目类型数组 */
    private String[] proTypes;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setProTypes(String[] proTypes)
    {
        this.proTypes = proTypes;
    }

    public String[] getProTypes()
    {
        return proTypes;
    }

    public void setResidentName(String residentName) 
    {
        this.residentName = residentName;
    }

    public String getResidentName() 
    {
        return residentName;
    }

    public void setResidentType(String residentType) 
    {
        this.residentType = residentType;
    }

    public String getResidentType() 
    {
        return residentType;
    }

    public void setCoordinate(String coordinate) 
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate() 
    {
        return coordinate;
    }

    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }

    public void setProjectType(String projectType) 
    {
        this.projectType = projectType;
    }

    public String getProjectType() 
    {
        return projectType;
    }

    public void setBuildUnit(String buildUnit) 
    {
        this.buildUnit = buildUnit;
    }

    public String getBuildUnit() 
    {
        return buildUnit;
    }

    public void setConstructionUnit(String constructionUnit) 
    {
        this.constructionUnit = constructionUnit;
    }

    public String getConstructionUnit() 
    {
        return constructionUnit;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setArea(String area) 
    {
        this.area = area;
    }

    public String getArea() 
    {
        return area;
    }

    public void setResidentsNum(Integer residentsNum) 
    {
        this.residentsNum = residentsNum;
    }

    public Integer getResidentsNum() 
    {
        return residentsNum;
    }

    public void setRiskLevel(String riskLevel) 
    {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() 
    {
        return riskLevel;
    }

    public void setRoomType(String roomType) 
    {
        this.roomType = roomType;
    }

    public String getRoomType() 
    {
        return roomType;
    }

    public void setHeadInv(Integer headInv) 
    {
        this.headInv = headInv;
    }

    public Integer getHeadInv() 
    {
        return headInv;
    }

    public void setIsCliff(Integer isCliff) 
    {
        this.isCliff = isCliff;
    }

    public Integer getIsCliff() 
    {
        return isCliff;
    }

    public void setIsCollapse(Integer isCollapse) 
    {
        this.isCollapse = isCollapse;
    }

    public Integer getIsCollapse() 
    {
        return isCollapse;
    }

    public void setIsRelocate(Integer isRelocate) 
    {
        this.isRelocate = isRelocate;
    }

    public Integer getIsRelocate() 
    {
        return isRelocate;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setWhistling(String whistling) 
    {
        this.whistling = whistling;
    }

    public String getWhistling() 
    {
        return whistling;
    }

    public void setWhistlingTel(String whistlingTel) 
    {
        this.whistlingTel = whistlingTel;
    }

    public String getWhistlingTel() 
    {
        return whistlingTel;
    }

    public void setBuilder(String builder) 
    {
        this.builder = builder;
    }

    public String getBuilder() 
    {
        return builder;
    }

    public void setBuilderId(Long builderId)
    {
        this.builderId = builderId;
    }

    public Long getBuilderId()
    {
        return builderId;
    }

    public void setBuilderTel(String builderTel) 
    {
        this.builderTel = builderTel;
    }

    public String getBuilderTel() 
    {
        return builderTel;
    }

    public void setConstruc(String construc) 
    {
        this.construc = construc;
    }

    public String getConstruc() 
    {
        return construc;
    }

    public void setConstrucTel(String construcTel) 
    {
        this.construcTel = construcTel;
    }

    public String getConstrucTel() 
    {
        return construcTel;
    }

    public void setAddresser(String addresser) 
    {
        this.addresser = addresser;
    }

    public String getAddresser() 
    {
        return addresser;
    }

    public void setAddresserTel(String addresserTel) 
    {
        this.addresserTel = addresserTel;
    }

    public String getAddresserTel() 
    {
        return addresserTel;
    }

    public void setCountyer(String countyer) 
    {
        this.countyer = countyer;
    }

    public String getCountyer() 
    {
        return countyer;
    }

    public void setCountyerTel(String countyerTel) 
    {
        this.countyerTel = countyerTel;
    }

    public String getCountyerTel() 
    {
        return countyerTel;
    }

    public void setMarketer(String marketer) 
    {
        this.marketer = marketer;
    }

    public String getMarketer() 
    {
        return marketer;
    }

    public void setMarketerTel(String marketerTel) 
    {
        this.marketerTel = marketerTel;
    }

    public String getMarketerTel() 
    {
        return marketerTel;
    }

    public void setProvincer(String provincer) 
    {
        this.provincer = provincer;
    }

    public String getProvincer() 
    {
        return provincer;
    }

    public void setProvincerTel(String provincerTel) 
    {
        this.provincerTel = provincerTel;
    }

    public String getProvincerTel() 
    {
        return provincerTel;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setCreateById(Long createById) 
    {
        this.createById = createById;
    }

    public Long getCreateById() 
    {
        return createById;
    }

    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("residentName", getResidentName())
            .append("residentType", getResidentType())
            .append("coordinate", getCoordinate())
            .append("projectName", getProjectName())
            .append("projectType", getProjectType())
            .append("buildUnit", getBuildUnit())
            .append("constructionUnit", getConstructionUnit())
            .append("address", getAddress())
            .append("area", getArea())
            .append("residentsNum", getResidentsNum())
            .append("riskLevel", getRiskLevel())
            .append("roomType", getRoomType())
            .append("headInv", getHeadInv())
            .append("isCliff", getIsCliff())
            .append("isCollapse", getIsCollapse())
            .append("isRelocate", getIsRelocate())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("whistling", getWhistling())
            .append("whistlingTel", getWhistlingTel())
            .append("builder", getBuilder())
            .append("builderId", getBuilderId())
            .append("builderTel", getBuilderTel())
            .append("construc", getConstruc())
            .append("construcTel", getConstrucTel())
            .append("addresser", getAddresser())
            .append("addresserTel", getAddresserTel())
            .append("countyer", getCountyer())
            .append("countyerTel", getCountyerTel())
            .append("marketer", getMarketer())
            .append("marketerTel", getMarketerTel())
            .append("provincer", getProvincer())
            .append("provincerTel", getProvincerTel())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("createById", getCreateById())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
