package com.tocc.system.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import com.tocc.system.mapper.SysDeptMapper;
import com.tocc.system.mapper.SysRoleMapper;
import com.tocc.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.common.annotation.DataScope;
import com.tocc.common.constant.UserConstants;
import com.tocc.common.core.domain.TreeSelect;
import com.tocc.common.core.domain.entity.SysDept;
import com.tocc.common.core.domain.entity.SysRole;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.core.text.Convert;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.spring.SpringUtils;
import java.util.stream.Collectors;
import java.util.ArrayList;

/**
 * 部门管理 服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SysDeptServiceImpl implements ISysDeptService
{
    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    /**
     * 查询部门管理数据
     * 
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(SysDept dept)
    {
        return deptMapper.selectDeptList(dept);
    }

    /**
     * 查询部门树结构信息
     *
     * @param dept 部门信息
     * @return 部门树信息集合
     */
    @Override
    public List<TreeSelect> selectDeptTreeList(SysDept dept)
    {
        List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
        return buildDeptTreeSelect(depts);
    }

    /**
     * 查询单位列表（包括单位和企业）
     *
     * @param dept 部门信息
     * @return 单位信息集合
     */
    @Override
    public List<SysDept> selectUnitList(SysDept dept)
    {
        // 查询单位和企业
        List<SysDept> result = new ArrayList<>();

        // 查询单位
        SysDept unitQuery = new SysDept();
        if (dept.getDeptName() != null) unitQuery.setDeptName(dept.getDeptName());
        if (dept.getStatus() != null) unitQuery.setStatus(dept.getStatus());
        if (dept.getParentId() != null) unitQuery.setParentId(dept.getParentId());
        unitQuery.setOrgType(UserConstants.ORG_TYPE_UNIT);
        List<SysDept> units = SpringUtils.getAopProxy(this).selectDeptList(unitQuery);
        result.addAll(units);

        // 查询企业
        SysDept enterpriseQuery = new SysDept();
        if (dept.getDeptName() != null) enterpriseQuery.setDeptName(dept.getDeptName());
        if (dept.getStatus() != null) enterpriseQuery.setStatus(dept.getStatus());
        if (dept.getParentId() != null) enterpriseQuery.setParentId(dept.getParentId());
        enterpriseQuery.setOrgType(UserConstants.ORG_TYPE_ENTERPRISE);
        List<SysDept> enterprises = SpringUtils.getAopProxy(this).selectDeptList(enterpriseQuery);
        result.addAll(enterprises);

        return result;
    }

    /**
     * 查询部门列表（只返回直接子部门，不包括子单位的部门）
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    public List<SysDept> selectDepartmentList(SysDept dept)
    {
        // 如果指定了父节点ID，只返回该父节点的直接子部门
        if (dept.getParentId() != null) {
            return deptMapper.selectDirectChildDepartments(dept.getParentId());
        }

        // 如果没有指定父节点，返回所有部门类型的节点
        dept.setOrgType(UserConstants.ORG_TYPE_DEPT); // 设置为部门类型
        return SpringUtils.getAopProxy(this).selectDeptList(dept);
    }

    /**
     * 查询单位树结构信息（包括单位和企业）
     *
     * @param dept 部门信息
     * @return 单位树信息集合（包括单位和企业）
     */
    @Override
    public List<TreeSelect> selectUnitTreeList(SysDept dept)
    {
        List<SysDept> units = selectUnitList(dept);
        return buildDeptTreeSelect(units);
    }

    /**
     * 查询部门树结构信息
     *
     * @param dept 部门信息
     * @return 部门树信息集合
     */
    @Override
    public List<TreeSelect> selectDepartmentTreeList(SysDept dept)
    {
        List<SysDept> departments = selectDepartmentList(dept);
        return buildDeptTreeSelect(departments);
    }

    /**
     * 查询单位-部门层级树形结构
     * 单位作为一级节点，部门作为二级节点
     *
     * @param dept 部门信息
     * @return 单位-部门树形结构集合
     */
    @Override
    public List<TreeSelect> selectUnitDeptTreeList(SysDept dept)
    {
        // 查询所有组织数据（包含单位和部门）
        List<SysDept> allOrgs = SpringUtils.getAopProxy(this).selectDeptList(dept);

        // 构建单位-部门层级树形结构
        return buildUnitDeptTreeSelect(allOrgs);
    }

    /**
     * 构建单位-企业-部门层级树形结构
     *
     * @param allOrgs 所有组织列表
     * @return 单位-企业-部门树形结构列表
     */
    private List<TreeSelect> buildUnitDeptTreeSelect(List<SysDept> allOrgs)
    {
        // 分离单位、企业和部门
        List<SysDept> units = allOrgs.stream()
                .filter(org -> UserConstants.ORG_TYPE_UNIT.equals(org.getOrgType()))
                .collect(Collectors.toList());

        List<SysDept> enterprises = allOrgs.stream()
                .filter(org -> UserConstants.ORG_TYPE_ENTERPRISE.equals(org.getOrgType()))
                .collect(Collectors.toList());

        List<SysDept> departments = allOrgs.stream()
                .filter(org -> UserConstants.ORG_TYPE_DEPT.equals(org.getOrgType()))
                .collect(Collectors.toList());

        // 构建单位树
        List<SysDept> unitTrees = buildDeptTree(units);

        // 构建企业树
        List<SysDept> enterpriseTrees = buildDeptTree(enterprises);

        // 为每个单位和企业添加其下属部门
        List<SysDept> allTopLevelOrgs = new ArrayList<>();
        allTopLevelOrgs.addAll(unitTrees);
        allTopLevelOrgs.addAll(enterpriseTrees);

        for (SysDept org : allTopLevelOrgs) {
            List<SysDept> orgDepts = departments.stream()
                    .filter(dept -> isUnderUnit(dept, org))
                    .collect(Collectors.toList());

            if (!orgDepts.isEmpty()) {
                List<SysDept> deptTrees = buildDeptTree(orgDepts);
                org.getChildren().addAll(deptTrees);
            }
        }

        return allTopLevelOrgs.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据单位ID查询该单位下的直接部门（不包括子单位的部门）
     *
     * @param unitId 单位ID（包括企业）
     * @return 部门列表
     */
    @Override
    public List<SysDept> selectDepartmentsByUnitId(Long unitId)
    {
        // 验证单位ID是否有效（包括单位和企业）
        SysDept unit = selectDeptById(unitId);
        if (unit == null || (!UserConstants.ORG_TYPE_UNIT.equals(unit.getOrgType()) && !UserConstants.ORG_TYPE_ENTERPRISE.equals(unit.getOrgType()))) {
            return new ArrayList<>();
        }

        // 直接查询该单位/企业的直接子部门
        return deptMapper.selectDirectChildDepartments(unitId);
    }



    /**
     * 判断部门是否直接属于指定单位（只判断直接子部门）
     *
     * @param dept 部门
     * @param unit 单位
     * @return 是否直接属于
     */
    private boolean isUnderUnit(SysDept dept, SysDept unit)
    {
        // 只判断部门的父ID是否是单位ID（直接子部门）
        return unit.getDeptId().equals(dept.getParentId());
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<SysDept> buildDeptTree(List<SysDept> depts)
    {
        List<SysDept> returnList = new ArrayList<SysDept>();
        List<Long> tempList = depts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
        for (SysDept dept : depts)
        {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId()))
            {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts)
    {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     * 
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Long> selectDeptListByRoleId(Long roleId)
    {
        SysRole role = roleMapper.selectRoleById(roleId);
        return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public SysDept selectDeptById(Long deptId)
    {
        return deptMapper.selectDeptById(deptId);
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     * 
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public int selectNormalChildrenDeptById(Long deptId)
    {
        return deptMapper.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在子节点
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId)
    {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    /**
     * 查询部门是否存在用户
     * 
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId)
    {
        int result = deptMapper.checkDeptExistUser(deptId);
        return result > 0;
    }

    /**
     * 校验部门名称是否唯一
     * 
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public boolean checkDeptNameUnique(SysDept dept)
    {
        Long deptId = StringUtils.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
        SysDept info = deptMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验部门是否有数据权限
     * 
     * @param deptId 部门id
     */
    @Override
    public void checkDeptDataScope(Long deptId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()) && StringUtils.isNotNull(deptId))
        {
            SysDept dept = new SysDept();
            dept.setDeptId(deptId);
            List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
            if (StringUtils.isEmpty(depts))
            {
                throw new ServiceException("没有权限访问部门数据！");
            }
        }
    }

    /**
     * 新增保存部门信息
     * 
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int insertDept(SysDept dept)
    {
        SysDept info = deptMapper.selectDeptById(dept.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus()))
        {
            throw new ServiceException("部门停用，不允许新增");
        }
        dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
        return deptMapper.insertDept(dept);
    }

    /**
     * 修改保存部门信息
     * 
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int updateDept(SysDept dept)
    {
        SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
        SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept))
        {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        int result = deptMapper.updateDept(dept);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
                && !StringUtils.equals("0", dept.getAncestors()))
        {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     * 
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept)
    {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        deptMapper.updateDeptStatusNormal(deptIds);
    }

    /**
     * 修改子元素关系
     * 
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors)
    {
        List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
        for (SysDept child : children)
        {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
            deptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public int deleteDeptById(Long deptId)
    {
        return deptMapper.deleteDeptById(deptId);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDept> list, SysDept t)
    {
        // 得到子节点列表
        List<SysDept> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDept tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDept> getChildList(List<SysDept> list, SysDept t)
    {
        List<SysDept> tlist = new ArrayList<SysDept>();
        Iterator<SysDept> it = list.iterator();
        while (it.hasNext())
        {
            SysDept n = (SysDept) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDept> list, SysDept t)
    {
        return getChildList(list, t).size() > 0;
    }
}
